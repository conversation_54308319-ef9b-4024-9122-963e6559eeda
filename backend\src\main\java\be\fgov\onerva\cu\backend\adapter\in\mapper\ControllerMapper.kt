package be.fgov.onerva.cu.backend.adapter.`in`.mapper

import be.fgov.onerva.cu.backend.application.domain.Address
import be.fgov.onerva.cu.backend.application.domain.AddressNullable
import be.fgov.onerva.cu.backend.application.domain.Annex
import be.fgov.onerva.cu.backend.application.domain.AnnexType
import be.fgov.onerva.cu.backend.application.domain.CitizenInformation
import be.fgov.onerva.cu.backend.application.domain.DecisionType
import be.fgov.onerva.cu.backend.application.domain.FieldSource
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenAuthenticSources
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenC1
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenOnem
import be.fgov.onerva.cu.backend.application.domain.ManualVerificationType
import be.fgov.onerva.cu.backend.application.domain.ModeOfPayment
import be.fgov.onerva.cu.backend.application.domain.RequestBasicInfo
import be.fgov.onerva.cu.backend.application.domain.RequestInformation
import be.fgov.onerva.cu.backend.application.domain.RoutingDecisionItem
import be.fgov.onerva.cu.backend.application.domain.SyncFollowUpStatus
import be.fgov.onerva.cu.backend.application.domain.UnionContribution
import be.fgov.onerva.cu.backend.application.domain.UpdateCitizenInformation
import be.fgov.onerva.cu.backend.application.domain.UpdateModeOfPayment
import be.fgov.onerva.cu.backend.application.domain.UpdateUnionContribution
import be.fgov.onerva.cu.backend.application.domain.WaveTask
import be.fgov.onerva.cu.backend.application.domain.WaveTaskStatus
import be.fgov.onerva.cu.backend.application.exception.InvalidInputException
import be.fgov.onerva.cu.rest.priv.model.CitizenInformationDetailResponse
import be.fgov.onerva.cu.rest.priv.model.ExternalSource
import be.fgov.onerva.cu.rest.priv.model.HistoricalCitizenAuthenticSourcesResponse
import be.fgov.onerva.cu.rest.priv.model.HistoricalCitizenC1Response
import be.fgov.onerva.cu.rest.priv.model.HistoricalCitizenOnemResponse
import be.fgov.onerva.cu.rest.priv.model.ModeOfPaymentDetailResponse
import be.fgov.onerva.cu.rest.priv.model.RequestBasicInfoResponse
import be.fgov.onerva.cu.rest.priv.model.RequestBasicInfoResponse.DecisionTypeEnum
import be.fgov.onerva.cu.rest.priv.model.RequestInformationResponse
import be.fgov.onerva.cu.rest.priv.model.RoutingDecisionItemResponse
import be.fgov.onerva.cu.rest.priv.model.RoutingDecisionUpdateRequest
import be.fgov.onerva.cu.rest.priv.model.UnionContributionDetailResponse
import be.fgov.onerva.cu.rest.priv.model.UnionContributionFields
import be.fgov.onerva.cu.rest.priv.model.UpdateCitizenInformationRequest
import be.fgov.onerva.cu.rest.priv.model.UpdateModeOfPaymentRequest
import be.fgov.onerva.cu.rest.priv.model.UpdateRequestInformationRequest
import be.fgov.onerva.cu.rest.priv.model.UpdateUnionContributionRequest
import be.fgov.onerva.cu.rest.priv.model.WaveTaskResponse
import be.fgov.onerva.cu.rest.priv.model.ManualVerificationType as ManualVerificationTypeResponse

fun ExternalSource.toDomainExternalSource() =
        when (this) {
                ExternalSource.C1 -> be.fgov.onerva.cu.backend.application.domain.ExternalSource.C1
                ExternalSource.ONEM ->
                        be.fgov.onerva.cu.backend.application.domain.ExternalSource.ONEM

                ExternalSource.AUTHENTIC_SOURCES ->
                        be.fgov.onerva.cu.backend.application.domain.ExternalSource
                                .AUTHENTIC_SOURCES
        }

fun SyncFollowUpStatus.toPushbackStatusResponse() =
        when (this) {
                SyncFollowUpStatus.PENDING -> RequestBasicInfoResponse.PushbackStatusEnum.PENDING
                SyncFollowUpStatus.OK -> RequestBasicInfoResponse.PushbackStatusEnum.OK
                SyncFollowUpStatus.NOK -> RequestBasicInfoResponse.PushbackStatusEnum.NOK
        }

fun be.fgov.onerva.cu.backend.application.domain.IdentityDocumentType.toDocumentTypeResponse() =
        when (this) {
                be.fgov.onerva.cu.backend.application.domain.IdentityDocumentType.ELECTRONIC ->
                        be.fgov.onerva.cu.rest.priv.model.RequestBasicInfoResponse.DocumentTypeEnum
                                .ELECTRONIC

                be.fgov.onerva.cu.backend.application.domain.IdentityDocumentType.PAPER ->
                        be.fgov.onerva.cu.rest.priv.model.RequestBasicInfoResponse.DocumentTypeEnum
                                .PAPER
        }

fun be.fgov.onerva.cu.backend.application.domain.ExternalSource.toExternalSourceResponse() =
        when (this) {
                be.fgov.onerva.cu.backend.application.domain.ExternalSource.C1 -> ExternalSource.C1
                be.fgov.onerva.cu.backend.application.domain.ExternalSource.ONEM ->
                        ExternalSource.ONEM

                be.fgov.onerva.cu.backend.application.domain.ExternalSource.AUTHENTIC_SOURCES ->
                        ExternalSource.AUTHENTIC_SOURCES
        }

fun FieldSource.toFieldSourceResponse() =
        be.fgov.onerva.cu.rest.priv.model.FieldSource().apply {
                this.fieldName = <EMAIL>
                this.source = <EMAIL>()
        }

fun be.fgov.onerva.cu.rest.priv.model.SelectFieldSourcesRequest.toDomainFieldSources() =
        this.fieldSources.map {
                FieldSource(fieldName = it.fieldName, source = it.source.toDomainExternalSource())
        }

fun UpdateCitizenInformationRequest.toDomainUpdateCitizenInformation() =
        UpdateCitizenInformation(
                birthDate = this.birthDate,
                nationalityCode = this.nationalityCode,
                address =
                        Address(
                                countryCode = this.address.countryCode,
                                street = this.address.street,
                                houseNumber = this.address.houseNumber,
                                boxNumber = this.address.boxNumber,
                                city = this.address.city,
                                zipCode = this.address.zipCode,
                                validFrom = this.address.validFrom
                        ),
        )

fun CitizenInformation.toCitizenInformationDetailResponse() =
        CitizenInformationDetailResponse().apply {
                birthDate = <EMAIL>
                nationalityCode = <EMAIL>
                address =
                        be.fgov.onerva.cu.rest.priv.model.Address().apply {
                                boxNumber =
                                        <EMAIL>
                                city = <EMAIL>
                                countryCode =
                                        <EMAIL>
                                houseNumber =
                                        <EMAIL>
                                street = <EMAIL>
                                zipCode = <EMAIL>
                                validFrom =
                                        <EMAIL>
                        }
        }

fun UpdateModeOfPaymentRequest.toDomainUpdateModeOfPayment(): UpdateModeOfPayment {
        if (this.iban == null) {
                throw InvalidInputException("Bank account is required")
        }
        return UpdateModeOfPayment(
                otherPersonName = this.otherPersonName,
                iban = this.iban,
                bic = this.bic,
                validFrom = this.validFrom,
        )
}

fun ModeOfPayment.toModeOfPaymentDetailResponse() =
        ModeOfPaymentDetailResponse()
                .otherPersonName(this.otherPersonName)
                .iban(this.iban)
                .bic(this.bic)
                .validFrom(this.validFrom)

fun UnionContribution.toUnionContributionDetailResponse() =
        UnionContributionDetailResponse()
                .authorized(this.authorized)
                .effectiveDate(this.effectiveDate)

fun UpdateUnionContributionRequest.toDomainUpdateUnionContribution() =
        UpdateUnionContribution(
                authorized = this.authorized,
                effectiveDate = this.effectiveDate,
        )

fun AnnexType.toAnnexType() =
        when (this) {
                AnnexType.SCANNED_DOCUMENTS ->
                        be.fgov.onerva.cu.rest.priv.model.AnnexType.SCANNED_DOCUMENTS

                AnnexType.EC1 -> be.fgov.onerva.cu.rest.priv.model.AnnexType.EC1
        }

fun Annex.toAnnexResponse(): be.fgov.onerva.cu.rest.priv.model.Annex =
        be.fgov.onerva.cu.rest.priv.model.Annex().type(this.type.toAnnexType()).url(this.url)

fun DecisionType.toDecisionTypeResponse(): DecisionTypeEnum = DecisionTypeEnum.fromValue(this.name)

fun RequestBasicInfo.toRequestBasicInfoResponse(): RequestBasicInfoResponse {

        return RequestBasicInfoResponse()
                .requestDate(this.requestDate)
                .ssin(this.ssin)
                .firstName(this.firstName)
                .lastName(this.lastName)
                .introductionDate(this.introductionDate)
                .dateValid(this.dateValid)
                .annexes(this.annexes.map(Annex::toAnnexResponse))
                .decisionType(this.decisionType?.toDecisionTypeResponse())
                .decisionBarema(this.decisionBarema)
                .c9Id(this.c9Id.toString())
                .pushbackStatus(this.pushbackStatus?.toPushbackStatusResponse())
                .documentType(this.documentType.toDocumentTypeResponse())
}

fun WaveTaskStatus.toResponseWaveTaskStatus() =
        when (this) {
                WaveTaskStatus.OPEN -> be.fgov.onerva.cu.rest.priv.model.WaveTaskStatus.OPEN
                WaveTaskStatus.CLOSED -> be.fgov.onerva.cu.rest.priv.model.WaveTaskStatus.CLOSED
                WaveTaskStatus.WAITING -> be.fgov.onerva.cu.rest.priv.model.WaveTaskStatus.WAITING
                WaveTaskStatus.DELETED -> be.fgov.onerva.cu.rest.priv.model.WaveTaskStatus.DELETED
        }

fun WaveTask.toWaveTaskResponse() =
        this.let {
                WaveTaskResponse()
                        .processId(it.processId)
                        .taskId(it.taskId)
                        .status(it.status.toResponseWaveTaskStatus())
                        .waveUrl(it.waveUrl)
        }

fun RequestInformation.toRequestInformationResponse(): RequestInformationResponse =
        RequestInformationResponse().requestDate(this.requestDate)

fun AddressNullable.toAddressNullable() =
        be.fgov.onerva.cu.rest.priv.model.AddressNullable().also {
                it.boxNumber = this.boxNumber
                it.city = this.city
                it.countryCode = this.countryCode
                it.houseNumber = this.houseNumber
                it.street = this.street
                it.zipCode = this.zipCode
        }

fun RoutingDecisionItem.toRoutingDecisionItemResponse(): RoutingDecisionItemResponse =
        RoutingDecisionItemResponse().also { response ->
                response.type = this.type.toManualVerificationTypeResponse()
                response.value = this.value
        }

fun RoutingDecisionItemResponse.toDomainRoutingDecisionItem(): RoutingDecisionItem =
        RoutingDecisionItem(
                type = this.type.toDomainManualVerificationType(),
                value = this.value,
        )

fun ManualVerificationTypeResponse.toDomainManualVerificationType(): ManualVerificationType =
        when (this) {
                ManualVerificationTypeResponse.CITIZEN_OVER_65_YEARS_OLD ->
                        ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD

                ManualVerificationTypeResponse.RELEVANT_TO_PORT_WORKER ->
                        ManualVerificationType.RELEVANT_TO_PORT_WORKER

                ManualVerificationTypeResponse.NON_BELGIAN_RESIDENT ->
                        ManualVerificationType.NON_BELGIAN_RESIDENT

                ManualVerificationTypeResponse.TRANSFER_BETWEEN_OP_OR_OC ->
                        ManualVerificationType.TRANSFER_BETWEEN_OP_OR_OC

                ManualVerificationTypeResponse.REQUEST_FOR_ECONOMIC_REASON ->
                        ManualVerificationType.REQUEST_FOR_ECONOMIC_REASON

                ManualVerificationTypeResponse.RELEVANT_TO_APPRENTICESHIP ->
                        ManualVerificationType.RELEVANT_TO_APPRENTICESHIP

                ManualVerificationTypeResponse.CASE_OF_IMPULSION ->
                        ManualVerificationType.CASE_OF_IMPULSION
        }

fun UnionContribution.toUnionContributionFields() =
        UnionContributionFields().also {
                it.authorized = this.authorized
                it.effectiveDate = this.effectiveDate
        }

fun HistoricalCitizenOnem.toHistoricalCitizenOnemResponse() =
        HistoricalCitizenOnemResponse().also {
                it.firstName = this.firstName
                it.lastName = this.lastName
                it.iban = this.iban
                it.bic = this.bic
                it.otherPersonName = this.otherPersonName
                it.birthDate = this.birthDate
                it.nationalityCode = this.nationalityCode
                it.address = this.address.toAddressNullable()
                it.bankAccountValueDate = this.bankAccountValueDate
                it.addressValueDate = this.address.valueDate
                it.unionContributionValueDate = this.effectiveDate
                it.unionDue =
                        UnionContribution(
                                authorized = this.authorized,
                                effectiveDate = this.effectiveDate
                        )
                                .toUnionContributionFields()
        }

fun HistoricalCitizenAuthenticSources.toHistoricalCitizenAuthenticSourcesResponse() =
        HistoricalCitizenAuthenticSourcesResponse().also {
                it.firstName = this.firstName
                it.lastName = this.lastName
                it.birthDate = this.birthDate
                it.nationalityCode = this.nationalityCode
                it.address = this.address.toAddressNullable()
                it.valueDate = this.address.valueDate
        }

fun HistoricalCitizenC1.toHistoricalCitizenC1Response() =
        HistoricalCitizenC1Response().also {
                it.firstName = this.firstName
                it.lastName = this.lastName
                it.iban = this.iban
                it.bic = this.bic
                it.otherPersonName = this.otherPersonName
                it.birthDate = this.birthDate
                it.nationalityCode = this.nationalityCode
                it.address = this.address.toAddressNullable()
                it.bankAccountValueDate = this.bankAccountValueDate
                it.addressValueDate = this.address.valueDate
                it.unionContributionValueDate = this.effectiveDate
                it.unionDue =
                        UnionContribution(
                                authorized = this.authorized,
                                effectiveDate = this.effectiveDate
                        )
                                .toUnionContributionFields()
        }

fun RoutingDecisionUpdateRequest.toDomainRoutingDecisionItem() =
        RoutingDecisionItem(type = this.type!!.toDomainManualVerificationType(), value = this.value)

fun be.fgov.onerva.cu.backend.application.domain.ManualVerificationType.toManualVerificationTypeResponse() =
        when (this) {
                be.fgov.onerva.cu.backend.application.domain.ManualVerificationType
                        .CITIZEN_OVER_65_YEARS_OLD,
                        ->
                        ManualVerificationTypeResponse.CITIZEN_OVER_65_YEARS_OLD

                be.fgov.onerva.cu.backend.application.domain.ManualVerificationType
                        .RELEVANT_TO_PORT_WORKER,
                        ->
                        ManualVerificationTypeResponse.RELEVANT_TO_PORT_WORKER

                be.fgov.onerva.cu.backend.application.domain.ManualVerificationType
                        .NON_BELGIAN_RESIDENT,
                        -> ManualVerificationTypeResponse.NON_BELGIAN_RESIDENT

                be.fgov.onerva.cu.backend.application.domain.ManualVerificationType
                        .TRANSFER_BETWEEN_OP_OR_OC,
                        ->
                        ManualVerificationTypeResponse.TRANSFER_BETWEEN_OP_OR_OC

                be.fgov.onerva.cu.backend.application.domain.ManualVerificationType
                        .REQUEST_FOR_ECONOMIC_REASON,
                        ->
                        ManualVerificationTypeResponse.REQUEST_FOR_ECONOMIC_REASON

                be.fgov.onerva.cu.backend.application.domain.ManualVerificationType
                        .RELEVANT_TO_APPRENTICESHIP,
                        ->
                        ManualVerificationTypeResponse.RELEVANT_TO_APPRENTICESHIP

                be.fgov.onerva.cu.backend.application.domain.ManualVerificationType
                        .CASE_OF_IMPULSION,
                        -> ManualVerificationTypeResponse.CASE_OF_IMPULSION
        }

fun UpdateRequestInformationRequest.toDomainRequestInformation() =
        RequestInformation(
                requestDate = this.requestDate
        )
