package be.fgov.onerva.cu.backend.adapter.`in`.controller

import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.backend.application.domain.ManualVerificationType
import be.fgov.onerva.cu.backend.application.domain.RoutingDecision
import be.fgov.onerva.cu.backend.application.domain.RoutingDecisionItem
import be.fgov.onerva.cu.backend.application.port.`in`.RoutingDecisionUseCase
import be.fgov.onerva.cu.rest.priv.model.RoutingDecisionUpdateRequest
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class RoutingDecisionControllerTest {

    @MockK
    lateinit var routingDecisionUseCase: RoutingDecisionUseCase

    @InjectMockKs
    lateinit var controller: RoutingDecisionController

    private val requestId = UUID.randomUUID()

    @Nested
    inner class GetRoutingDecisionTests {

        @Test
        fun `should get routing decisions successfully with processInWave true`() {
            // Given
            val routingDecisionItems = setOf(
                RoutingDecisionItem(ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD, false),
                RoutingDecisionItem(ManualVerificationType.NON_BELGIAN_RESIDENT, false)
            )
            val routingDecision = RoutingDecision(true, routingDecisionItems)
            
            every { routingDecisionUseCase.getRoutingDecisions(requestId) } returns routingDecision

            // When
            val response = controller.getRoutingDecision(requestId)

            // Then
            assertThat(response.processInWave).isTrue
            assertThat(response.routingDecisions).hasSize(2)
            assertThat(response.routingDecisions[0].type.toString()).isEqualTo("CITIZEN_OVER_65_YEARS_OLD")
            assertThat(response.routingDecisions[0].value).isFalse
            assertThat(response.routingDecisions[1].type.toString()).isEqualTo("NON_BELGIAN_RESIDENT")
            assertThat(response.routingDecisions[1].value).isFalse
            
            verify(exactly = 1) { routingDecisionUseCase.getRoutingDecisions(requestId) }
        }

        @Test
        @Disabled
        fun `should get routing decisions successfully with processInWave false`() {
            // Given
            val routingDecisionItems = setOf(
                RoutingDecisionItem(ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD, false),
                RoutingDecisionItem(ManualVerificationType.NON_BELGIAN_RESIDENT, null)
            )
            val routingDecision = RoutingDecision(false, routingDecisionItems)
            
            every { routingDecisionUseCase.getRoutingDecisions(requestId) } returns routingDecision

            // When
            val response = controller.getRoutingDecision(requestId)

            // Then
            assertThat(response.processInWave).isFalse
            assertThat(response.routingDecisions).hasSize(2)
            assertThat(response.routingDecisions[0].type.toString()).isEqualTo("CITIZEN_OVER_65_YEARS_OLD")
            assertThat(response.routingDecisions[0].value).isFalse
            assertThat(response.routingDecisions[1].type.toString()).isEqualTo("NON_BELGIAN_RESIDENT")
            assertThat(response.routingDecisions[1].value).isNull()
            
            verify(exactly = 1) { routingDecisionUseCase.getRoutingDecisions(requestId) }
        }

        @Test
        @Disabled
        fun `should get routing decisions successfully with processInWave null`() {
            // Given
            val routingDecisionItems = setOf(
                RoutingDecisionItem(ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD, null),
                RoutingDecisionItem(ManualVerificationType.NON_BELGIAN_RESIDENT, null)
            )
            val routingDecision = RoutingDecision(null, routingDecisionItems)
            
            every { routingDecisionUseCase.getRoutingDecisions(requestId) } returns routingDecision

            // When
            val response = controller.getRoutingDecision(requestId)

            // Then
            assertThat(response.processInWave).isNull()
            assertThat(response.routingDecisions).hasSize(2)
            assertThat(response.routingDecisions[0].type.toString()).isEqualTo("CITIZEN_OVER_65_YEARS_OLD")
            assertThat(response.routingDecisions[0].value).isNull()
            assertThat(response.routingDecisions[1].type.toString()).isEqualTo("NON_BELGIAN_RESIDENT")
            assertThat(response.routingDecisions[1].value).isNull()
            
            verify(exactly = 1) { routingDecisionUseCase.getRoutingDecisions(requestId) }
        }

        @Test
        @Disabled
        fun `should get routing decisions successfully with empty decisions`() {
            // Given
            val routingDecision = RoutingDecision(null, emptySet())
            
            every { routingDecisionUseCase.getRoutingDecisions(requestId) } returns routingDecision

            // When
            val response = controller.getRoutingDecision(requestId)

            // Then
            assertThat(response.processInWave).isNull()
            assertThat(response.routingDecisions).isEmpty()
            
            verify(exactly = 1) { routingDecisionUseCase.getRoutingDecisions(requestId) }
        }

        @Test
        @Disabled
        fun `should throw exception when requestId is null`() {
            // When/Then
            assertThatThrownBy {
                controller.getRoutingDecision(null)
            }.isInstanceOf(KotlinNullPointerException::class.java)
        }
    }

    @Nested
    inner class UpdateRoutingDecisionTests {

        @Test
        fun `should update routing decisions successfully`() {
            // Given
            val updateRequests = listOf(
                RoutingDecisionUpdateRequest().apply {
                    type = be.fgov.onerva.cu.rest.priv.model.ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD
                    value = false
                },
                RoutingDecisionUpdateRequest().apply {
                    type = be.fgov.onerva.cu.rest.priv.model.ManualVerificationType.NON_BELGIAN_RESIDENT
                    value = true
                }
            )
            
            every { routingDecisionUseCase.updateRoutingDecisions(any(), any()) } returns Unit

            // When
            controller.updateRoutingDecision(requestId, updateRequests)

            // Then
            verify(exactly = 1) { 
                routingDecisionUseCase.updateRoutingDecisions(
                    eq(requestId), 
                    match { decisions ->
                        decisions.size == 2 &&
                        decisions.any { it.type == ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD && it.value == false } &&
                        decisions.any { it.type == ManualVerificationType.NON_BELGIAN_RESIDENT && it.value == true }
                    }
                ) 
            }
        }

        @Test
        fun `should update routing decisions successfully with null values`() {
            // Given
            val updateRequests = listOf(
                RoutingDecisionUpdateRequest().apply {
                    type = be.fgov.onerva.cu.rest.priv.model.ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD
                    value = null
                },
                RoutingDecisionUpdateRequest().apply {
                    type = be.fgov.onerva.cu.rest.priv.model.ManualVerificationType.NON_BELGIAN_RESIDENT
                    value = false
                }
            )

            every { routingDecisionUseCase.updateRoutingDecisions(any(), any()) } returns Unit

            // When
            controller.updateRoutingDecision(requestId, updateRequests)

            // Then
            verify(exactly = 1) {
                routingDecisionUseCase.updateRoutingDecisions(
                    eq(requestId),
                    match { decisions ->
                        decisions.size == 2 &&
                        decisions.any { it.type == ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD && it.value == null } &&
                        decisions.any { it.type == ManualVerificationType.NON_BELGIAN_RESIDENT && it.value == false }
                    }
                )
            }
        }

        @Test
        fun `should update routing decisions successfully with empty list`() {
            // Given
            val updateRequests = emptyList<RoutingDecisionUpdateRequest>()

            every { routingDecisionUseCase.updateRoutingDecisions(any(), any()) } returns Unit

            // When
            controller.updateRoutingDecision(requestId, updateRequests)

            // Then
            verify(exactly = 1) {
                routingDecisionUseCase.updateRoutingDecisions(
                    eq(requestId),
                    match { decisions -> decisions.isEmpty() }
                )
            }
        }

        @Test
        @Disabled
        fun `should throw exception when requestId is null in update`() {
            // Given
            val updateRequests = listOf(
                RoutingDecisionUpdateRequest().apply {
                    type = be.fgov.onerva.cu.rest.priv.model.ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD
                    value = false
                }
            )

            // When/Then
            assertThatThrownBy {
                controller.updateRoutingDecision(null, updateRequests)
            }.isInstanceOf(KotlinNullPointerException::class.java)
        }

        @Test
        @Disabled
        fun `should throw exception when routingDecisionRequest is null`() {
            // When/Then
            assertThatThrownBy {
                controller.updateRoutingDecision(requestId, null)
            }.isInstanceOf(KotlinNullPointerException::class.java)
        }

        @Test
        fun `should update routing decisions with all manual verification types`() {
            // Given
            val updateRequests = listOf(
                RoutingDecisionUpdateRequest().apply {
                    type = be.fgov.onerva.cu.rest.priv.model.ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD
                    value = false
                },
                RoutingDecisionUpdateRequest().apply {
                    type = be.fgov.onerva.cu.rest.priv.model.ManualVerificationType.RELEVANT_TO_PORT_WORKER
                    value = false
                },
                RoutingDecisionUpdateRequest().apply {
                    type = be.fgov.onerva.cu.rest.priv.model.ManualVerificationType.NON_BELGIAN_RESIDENT
                    value = false
                },
                RoutingDecisionUpdateRequest().apply {
                    type = be.fgov.onerva.cu.rest.priv.model.ManualVerificationType.TRANSFER_BETWEEN_OP_OR_OC
                    value = false
                },
                RoutingDecisionUpdateRequest().apply {
                    type = be.fgov.onerva.cu.rest.priv.model.ManualVerificationType.REQUEST_FOR_ECONOMIC_REASON
                    value = false
                },
                RoutingDecisionUpdateRequest().apply {
                    type = be.fgov.onerva.cu.rest.priv.model.ManualVerificationType.RELEVANT_TO_APPRENTICESHIP
                    value = false
                },
                RoutingDecisionUpdateRequest().apply {
                    type = be.fgov.onerva.cu.rest.priv.model.ManualVerificationType.CASE_OF_IMPULSION
                    value = false
                }
            )

            every { routingDecisionUseCase.updateRoutingDecisions(any(), any()) } returns Unit

            // When
            controller.updateRoutingDecision(requestId, updateRequests)

            // Then
            verify(exactly = 1) {
                routingDecisionUseCase.updateRoutingDecisions(
                    eq(requestId),
                    match { decisions -> decisions.size == 7 }
                )
            }
        }
    }
}
