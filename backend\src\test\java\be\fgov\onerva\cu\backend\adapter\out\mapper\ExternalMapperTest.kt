package be.fgov.onerva.cu.backend.adapter.out.mapper

import java.math.BigDecimal
import java.time.LocalDate
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.parallel.Execution
import org.junit.jupiter.api.parallel.ExecutionMode
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import be.fgov.onerva.cu.backend.application.domain.Address
import be.fgov.onerva.cu.backend.application.domain.ModeOfPayment
import be.fgov.onerva.cu.backend.application.domain.UnionContribution
import be.fgov.onerva.cu.backend.application.domain.UpdateCitizenCommand
import be.fgov.onerva.cu.backend.application.domain.WaveTaskStatus
import be.fgov.onerva.cu.backend.application.exception.InvalidExternalDataException
import be.fgov.onerva.cu.backend.application.exception.WaveTaskStatusException
import be.fgov.onerva.cu.backend.wo.dto.WoTaskDTO
import be.fgov.onerva.person.rest.model.BankAccountDTO
import be.fgov.onerva.person.rest.model.CitizenDTO
import be.fgov.onerva.person.rest.model.CitizenInfoDTO
import be.fgov.onerva.person.rest.model.CitizenInfoUnionDueDTO
import be.fgov.onerva.person.rest.model.ForeignAddressDTO
import be.fgov.onerva.registerproxyservice.rest.model.Period
import be.fgov.onerva.registerproxyservice.rest.model.RegisterAddress
import be.fgov.onerva.registerproxyservice.rest.model.RegisterNationality
import be.fgov.onerva.registerproxyservice.rest.model.RegisterPerson
import be.fgov.onerva.registerproxyservice.rest.model.RegisterPersonNames
import be.fgov.onerva.registerproxyservice.rest.model.ResidentialAddress

@Execution(ExecutionMode.SAME_THREAD)
class CitizenMapperTest {
    @Nested
    inner class ToCitizen {

        @Test
        fun `should map CitizenDTO to Citizen with all fields populated`() {
            // Given
            val citizenDTO = CitizenDTO(
                firstname = "John",
                lastname = "Doe",
                numbox = 42,
                zipCode = 1000
            )

            // When
            val result = citizenDTO.toDomainCitizen()

            // Then
            assertThat(result)
                .isNotNull()
                .extracting("firstName", "lastName", "numbox", "zipCode")
                .containsExactly("John", "Doe", 42, "1000")
        }

        @Test
        fun `should map CitizenDTO to Citizen with null zipCode`() {
            // Given
            val citizenDTO = CitizenDTO(
                firstname = "John",
                lastname = "Doe",
                numbox = 42,
                zipCode = null
            )

            // When
            val result = citizenDTO.toDomainCitizen()

            // Then
            assertThat(result)
                .isNotNull()
                .extracting("firstName", "lastName", "numbox", "zipCode")
                .containsExactly("John", "Doe", 42, "")
        }

        @Test
        fun `should preserve zipCode value during conversion to string`() {
            // Given
            val zipCode = 1234
            val citizenDTO = CitizenDTO(
                firstname = "John",
                lastname = "Doe",
                numbox = 42,
                zipCode = zipCode
            )

            // When
            val result = citizenDTO.toDomainCitizen()

            // Then
            assertThat(result.zipCode).isEqualTo(zipCode.toString())
        }
    }

    @Nested
    inner class StatusToTaskStatus {
        @Test
        fun `statusToTaskStatus should return OPEN when status is OPEN`() {
            // Given
            val dto = WoTaskDTO("123", "456", "OPEN", "Treatment")

            // When
            val result = dto.toDomainTaskStatus()

            // Then
            assertThat(result).isEqualTo(WaveTaskStatus.OPEN)
        }

        @Test
        fun `statusToTaskStatus should return CLOSED when status is CLOSED`() {
            // Given
            val dto = WoTaskDTO("123", "456", "CLOSED", "Treatment")

            // When
            val result = dto.toDomainTaskStatus()

            // Then
            assertThat(result).isEqualTo(WaveTaskStatus.CLOSED)
        }

        @ParameterizedTest
        @ValueSource(strings = ["", "UNKNOWN", "IN_PROGRESS", "PENDING"])
        fun `statusToTaskStatus should throw WaveTaskStatusException for invalid status`(status: String) {
            // Given
            val dto = WoTaskDTO("123", "456", status, "Treatment")

            // When/Then
            assertThatThrownBy { dto.toDomainTaskStatus() }
                .isInstanceOf(WaveTaskStatusException::class.java)
                .hasMessage("Unknown task status: $status")
        }

        @Test
        fun `statusToTaskStatus should throw WaveTaskStatusException for null status`() {
            // Given
            val dto = WoTaskDTO("123", "456", null, "Treatment")

            // When/Then
            assertThatThrownBy { dto.toDomainTaskStatus() }
                .isInstanceOf(WaveTaskStatusException::class.java)
                .hasMessage("Unknown task status: null")
        }
    }

    @Nested
    inner class ToCitizenInfoWithAddress {

        @Nested
        @DisplayName("toCitizenInfoWithAddress")
        inner class ToCitizenInfoWithAddress {

            @Test
            @DisplayName("should map DTO with complete address correctly - paymentMode 2 - with holder")
            fun shouldMapDtoWithCompleteAddressCorrectly_paymentMode_2() {
                // Given
                val address = "Rue de la Loi 16"

                val dto = CitizenInfoDTO(
                    firstName = "John",
                    lastName = "Doe",
                    numBox = BigDecimal.valueOf(42),
                    flagNation = BigDecimal.valueOf(111),
                    iban = "****************",
                    address = address,
                    postalCode = "1000",
                    paymentMode = 2,
                    bankAccount = BankAccountDTO(
                        iban = "****************",
                        bic = "THEBIC",
                        holder = "The Holder"
                    ),
                    unionDue = CitizenInfoUnionDueDTO(
                        mandateActive = false,
                        validFrom = LocalDate.of(2022, 1, 1)
                    ),
                    addressObj = ForeignAddressDTO(
                        city = "Brussels",
                        street = "Main Street",
                        box = "Box A",
                        countryCode = 1,
                        zip = "1000",
                        number = "123",
                        validFrom = LocalDate.of(2022, 1, 1)
                    )
                )

                // When
                val result = dto.toCitizenInfoWithAddress(150)

                // Then
                assertThat(result).isNotNull
                assertThat(result.firstName).isEqualTo("John")
                assertThat(result.lastName).isEqualTo("Doe")
                assertThat(result.numbox).isEqualTo(42)
                assertThat(result.nationalityCode).isEqualTo(111)
                assertThat(result.iban).isEqualTo("****************")
                assertThat(result.bic).isEqualTo("THEBIC")
                assertThat(result.otherPersonName).isEqualTo("The Holder")

                assertThat(result.address).isNotNull
                assertThat(result.address.street).isEqualTo("Main Street")
                assertThat(result.address.houseNumber).isEqualTo("123")
                assertThat(result.address.boxNumber).isEqualTo("Box A")
                assertThat(result.address.countryCode).isEqualTo(150)
                assertThat(result.address.zipCode).isEqualTo("1000")
                assertThat(result.address.city).isEqualTo("Brussels")
            }

            @Test
            @DisplayName("should map DTO with complete address correctly - paymentMode 2 - without holder")
            fun shouldMapDtoWithCompleteAddressCorrectly_paymentMode_2_without_holder() {
                // Given
                val address = "Rue de la Loi 16"

                val dto = CitizenInfoDTO(
                    firstName = "John",
                    lastName = "Doe",
                    numBox = BigDecimal.valueOf(42),
                    flagNation = BigDecimal.valueOf(111),
                    iban = "****************",
                    address = address,
                    postalCode = "1000",
                    paymentMode = 2,
                    bankAccount = BankAccountDTO(
                        iban = "****************",
                        bic = "THEBIC",
                    ),
                    addressObj = ForeignAddressDTO(
                        city = "Brussels",
                        street = "Main Street",
                        box = "Box A",
                        countryCode = 1,
                        zip = "1000",
                        number = "123",
                        validFrom = LocalDate.of(2022, 1, 1)
                    ),
                    unionDue = CitizenInfoUnionDueDTO(
                        mandateActive = false,
                        validFrom = LocalDate.of(
                            2022,
                            1,
                            1
                        )
                    )
                )

                // When
                val result = dto.toCitizenInfoWithAddress(150)

                // Then
                assertThat(result).isNotNull
                assertThat(result.firstName).isEqualTo("John")
                assertThat(result.lastName).isEqualTo("Doe")
                assertThat(result.numbox).isEqualTo(42)
                assertThat(result.nationalityCode).isEqualTo(111)
                assertThat(result.iban).isEqualTo("****************")
                assertThat(result.bic).isEqualTo("THEBIC")
                assertThat(result.otherPersonName).isEqualTo("John Doe")

                assertThat(result.address).isNotNull
                assertThat(result.address.street).isEqualTo("Main Street")
                assertThat(result.address.houseNumber).isEqualTo("123")
                assertThat(result.address.boxNumber).isEqualTo("Box A")
                assertThat(result.address.countryCode).isEqualTo(150)
                assertThat(result.address.zipCode).isEqualTo("1000")
                assertThat(result.address.city).isEqualTo("Brussels")
            }

            @Test
            @DisplayName("should map DTO with complete address correctly - paymentMode 1")
            fun shouldMapDtoWithCompleteAddressCorrectly() {
                // Given
                val address = "Rue de la Loi 16"
                val dto = CitizenInfoDTO(
                    firstName = "John",
                    lastName = "Doe",
                    numBox = BigDecimal.valueOf(42),
                    flagNation = BigDecimal.valueOf(111),
                    iban = "****************",
                    address = address,
                    postalCode = "1000",
                    paymentMode = 1,
                    bankAccount = BankAccountDTO(
                        iban = "****************",
                        bic = "THEBIC",
                    ),
                    addressObj = ForeignAddressDTO(
                        city = "Brussels",
                        street = "Main Street",
                        box = "Box A",
                        countryCode = 1,
                        zip = "1000",
                        number = "123",
                        validFrom = LocalDate.of(2022, 1, 1)
                    ),
                    unionDue = CitizenInfoUnionDueDTO(
                        mandateActive = false,
                        validFrom =
                            LocalDate.of(2022, 1, 1)
                    )
                )

                // When
                val result = dto.toCitizenInfoWithAddress(150)

                // Then
                assertThat(result).isNotNull
                assertThat(result.firstName).isEqualTo("John")
                assertThat(result.lastName).isEqualTo("Doe")
                assertThat(result.numbox).isEqualTo(42)
                assertThat(result.nationalityCode).isEqualTo(111)
                assertThat(result.iban).isEqualTo("****************")
                assertThat(result.bic).isEqualTo("THEBIC")
                assertThat(result.otherPersonName).isNull()

                assertThat(result.address).isNotNull
                assertThat(result.address.street).isEqualTo("Main Street")
                assertThat(result.address.houseNumber).isEqualTo("123")
                assertThat(result.address.boxNumber).isEqualTo("Box A")
                assertThat(result.address.countryCode).isEqualTo(150)
                assertThat(result.address.zipCode).isEqualTo("1000")
                assertThat(result.address.city).isEqualTo("Brussels")
            }

            @Test
            @DisplayName("should handle null address correctly")
            fun shouldHandleNullAddressCorrectly() {
                // Given
                val dto = CitizenInfoDTO(
                    firstName = "Alice",
                    lastName = "Johnson",
                    numBox = null,
                    flagNation = null,
                    iban = "****************",
                    postalCode = "2000",
                    address = null,
                )

                // When / Then
                assertThatThrownBy {
                    dto.toCitizenInfoWithAddress(null)
                }.isInstanceOf(InvalidExternalDataException::class.java)
            }

            @Test
            @DisplayName("should handle RegisterPerson")
            fun shouldHandleRegisterPerson() {
                // Given
                val dto = RegisterPerson().apply {
                    addresses = listOf(
                        ResidentialAddress().apply {
                            atType = RegisterAddress.AtTypeEnum.RESIDENTIAL_ADDRESS
                            validityPeriod =
                                Period().apply {
                                    beginDate = 1490565600000
                                    endDate = null
                                }
                            radiated = false
                            cityCode = 21015
                            streetCode = 329
                            postalCode = "1070"
                            houseNumber = "37"
                            boxNumber = "ET01"
                            countryCode = 1
                            regionCode = "9"
                        }
                    )
                    birthdate = LocalDate.of(2000, 1, 1)
                    deceaseDate = null
                    gender = null
                    identitification = 0
                    lastName = "Doe"
                    names = listOf(
                        RegisterPersonNames().apply {
                            firstName = "John"
                            seq = 1
                        },
                        RegisterPersonNames().apply {
                            firstName = "Deere"
                            seq = 2
                        }
                    )
                    nationalities = listOf(
                        RegisterNationality().apply {
                            nationalityCode = 150
                            validityBeginDate = 946684800
                        }
                    )
                    replacedSsin = null
                    ssin = "85050599890"
                    status = null
                }

                // When
                val result = dto.toCitizenInfoWithAddress()
                // Then
                assertThat(result).isNotNull
                assertThat(result.firstName).isEqualTo("John")
                assertThat(result.lastName).isEqualTo("Doe")
                assertThat(result.nationalityCode).isEqualTo(150)
                assertThat(result.birthDate).isEqualTo(LocalDate.of(2000, 1, 1))

                assertThat(result.address).isNotNull
                assertThat(result.address.street).isEqualTo("")
                assertThat(result.address.houseNumber).isEqualTo("37")
                assertThat(result.address.boxNumber).isEqualTo("ET01")
                assertThat(result.address.countryCode).isNull()
                assertThat(result.address.zipCode).isEqualTo("1070")
                assertThat(result.address.city).isEqualTo(null)
            }

            @Test
            @DisplayName("should thorw an error when there is no first name")
            fun shouldThrowAnErroWhenThereIsNoFirstName() {
                // Given
                val dto = RegisterPerson().apply {
                    addresses = listOf(
                        ResidentialAddress().apply {
                            atType = RegisterAddress.AtTypeEnum.RESIDENTIAL_ADDRESS
                            validityPeriod =
                                Period().apply {
                                    beginDate = 1490565600000
                                    endDate = null
                                }
                            radiated = false
                            cityCode = 21015
                            streetCode = 329
                            postalCode = "1070"
                            houseNumber = "37"
                            boxNumber = "ET01"
                            countryCode = 1
                            regionCode = "9"
                        }
                    )
                    birthdate = LocalDate.of(2000, 1, 1)
                    deceaseDate = null
                    gender = null
                    identitification = 0
                    lastName = "Doe"
                    names = listOf(
                        RegisterPersonNames().apply {
                            firstName = "John"
                            seq = 2
                        },
                        RegisterPersonNames().apply {
                            firstName = "Deere"
                            seq = 3
                        }
                    )
                    nationalities = listOf(
                        RegisterNationality().apply {
                            nationalityCode = 150
                            validityBeginDate = 946684800
                        }
                    )
                    replacedSsin = null
                    ssin = "85050599890"
                    status = null
                }

                // When / Then
                assertThatThrownBy { dto.toCitizenInfoWithAddress() }
                    .isInstanceOf(InvalidExternalDataException::class.java)
                    .hasMessage("No name with sequence number 1 found")
            }

            @Test
            @DisplayName("should thorw an error when there is no residential adres")
            fun shouldThrowAnErrorWhenThereIsNoResidentialAdres() {
                // Given
                val dto = RegisterPerson().apply {
                    addresses = listOf(
                        ResidentialAddress().apply {
                            atType = RegisterAddress.AtTypeEnum.CONTACT_ADDRESS
                            validityPeriod =
                                Period().apply {
                                    beginDate = 1490565600000
                                    endDate = null
                                }
                            radiated = false
                            cityCode = 21015
                            streetCode = 329
                            postalCode = "1070"
                            houseNumber = "37"
                            boxNumber = "ET01"
                            countryCode = 1
                            regionCode = "9"
                        }
                    )
                    birthdate = LocalDate.of(2000, 1, 1)
                    deceaseDate = null
                    gender = null
                    identitification = 0
                    lastName = "Doe"
                    names = listOf(
                        RegisterPersonNames().apply {
                            firstName = "John"
                            seq = 1
                        },
                        RegisterPersonNames().apply {
                            firstName = "Deere"
                            seq = 2
                        }
                    )
                    nationalities = listOf(
                        RegisterNationality().apply {
                            nationalityCode = 150
                            validityBeginDate = 946684800
                        }
                    )
                    replacedSsin = null
                    ssin = "85050599890"
                    status = null
                }

                // When / Then
                assertThatThrownBy { dto.toCitizenInfoWithAddress() }
                    .isInstanceOf(InvalidExternalDataException::class.java)
                    .hasMessage("No residential address found")
            }
        }
    }

    @Nested
    inner class ToCitizenUpdateRequestDTO {

        @Test
        fun `should map UpdateCitizen to CitizenUpdateRequestDTO with all fields populated`() {
            // Given
            val requestDate = LocalDate.of(2024, 1, 1)
            val addressValidFrom = LocalDate.of(2022, 1, 1)
            val address = Address(
                street = "Main Street",
                houseNumber = "123",
                boxNumber = "Box A",
                countryCode = 56,
                city = "Brussels",
                zipCode = "1000",
                validFrom = addressValidFrom,
            )
            val updateCitizenCommand = UpdateCitizenCommand(
                ssin = "85050599890",
                address = address,
                birthDate = LocalDate.of(1985, 5, 5),
                nationalityCode = 150,
                correlationId = "corr-123",
                userName = "testuser",
                valueDate = LocalDate.of(2023, 1, 15),
                modeOfPayment = ModeOfPayment(
                    iban = "****************",
                    bic = "KREDBEBB",
                    otherPersonName = null,
                    validFrom = LocalDate.of(2023, 1, 15),
                ),
                unionContribution = UnionContribution(
                    authorized = true,
                    effectiveDate = LocalDate.of(2023, 1, 15),
                ),
                requestDate = requestDate,
                unemploymentOffice = 721,
            )

            // When
            val onemCountryCode = 1 // Converted ONEM country code
            val result = updateCitizenCommand.toCitizenUpdateRequestDTO(onemCountryCode)

            // Then
            assertThat(result).isNotNull
            assertThat(result.validFrom).isEqualTo(LocalDate.of(2023, 1, 15))
            assertThat(result.nationalityCode).isEqualTo(150)
            assertThat(result.birthDate).isEqualTo(LocalDate.of(1985, 5, 5))
            assertThat(result.correlationId).isEqualTo("corr-123")

            assertThat(result.address).isNotNull
            assertThat(result.address!!.street).isEqualTo("Main Street")
            assertThat(result.address!!.number).isEqualTo("123")
            assertThat(result.address!!.box).isEqualTo("Box A")
            assertThat(result.address!!.zip).isEqualTo("1000")
            assertThat(result.address!!.city).isEqualTo("Brussels")
            assertThat(result.address!!.countryCode).isEqualTo(1) // Should be the ONEM country code
            assertThat(result.address!!.validFrom).isEqualTo(addressValidFrom)

            assertThat(result.unionDueInfo).isNotNull
            assertThat(result.unionDueInfo!!.unionDue).isTrue()
            assertThat(result.unionDueInfo!!.validFrom).isEqualTo(LocalDate.of(2023, 1, 15))

            assertThat(result.unemploymentOffice).isEqualTo(721)
        }

        @Test
        fun `should map UpdateCitizen to CitizenUpdateRequestDTO with authorized false`() {
            // Given
            val requestDate = LocalDate.of(2024, 1, 1)
            val addressValidFrom = LocalDate.of(2022, 1, 1)
            val address = Address(
                street = "Second Street",
                houseNumber = "456",
                boxNumber = "Apt 2B",
                countryCode = 112,
                city = "Antwerp",
                zipCode = "2000",
                validFrom = addressValidFrom,
            )
            val updateCitizenCommand = UpdateCitizenCommand(
                ssin = "85050599890",
                address = address,
                birthDate = LocalDate.of(1990, 10, 15),
                nationalityCode = 112,
                correlationId = "corr-456",
                userName = "testuser2",
                valueDate = LocalDate.of(2023, 2, 20),
                modeOfPayment = ModeOfPayment(
                    iban = "****************",
                    bic = "KREDBEBB",
                    otherPersonName = null,
                    validFrom = LocalDate.of(2023, 1, 15),
                ),
                unionContribution = UnionContribution(
                    authorized = false,
                    effectiveDate = LocalDate.of(2023, 2, 20),
                ),
                requestDate = requestDate,
                unemploymentOffice = 721,
            )

            // When
            val onemCountryCode = 1 // Converted ONEM country code
            val result = updateCitizenCommand.toCitizenUpdateRequestDTO(onemCountryCode)

            // Then
            assertThat(result).isNotNull
            assertThat(result.validFrom).isEqualTo(LocalDate.of(2023, 2, 20))
            assertThat(result.nationalityCode).isEqualTo(112)
            assertThat(result.birthDate).isEqualTo(LocalDate.of(1990, 10, 15))
            assertThat(result.correlationId).isEqualTo("corr-456")

            assertThat(result.address).isNotNull
            assertThat(result.address!!.street).isEqualTo("Second Street")
            assertThat(result.address!!.number).isEqualTo("456")
            assertThat(result.address!!.box).isEqualTo("Apt 2B")
            assertThat(result.address!!.zip).isEqualTo("2000")
            assertThat(result.address!!.city).isEqualTo("Antwerp")
            assertThat(result.address!!.countryCode).isEqualTo(1) // Should be the ONEM country code
            assertThat(result.address!!.validFrom).isEqualTo(addressValidFrom)

            assertThat(result.unionDueInfo).isNotNull
            assertThat(result.unionDueInfo!!.unionDue).isFalse()
            assertThat(result.unionDueInfo!!.validFrom).isEqualTo(LocalDate.of(2023, 2, 20))

            assertThat(result.unemploymentOffice).isEqualTo(721)
        }
    }
}